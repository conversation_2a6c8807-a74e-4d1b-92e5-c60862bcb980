import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Typography, Divider } from 'antd';
import InputSection from './InputSection';
import ResultSection from './ResultSection';
import { calculatePricing, calculateTax } from '../utils/calculations';
import './Calculator.css';

const { Title } = Typography;

const Calculator = () => {
  // 输入参数状态
  const [params, setParams] = useState({
    transportCost: 4000,      // 运输费用（元）
    laborCost: 1000,          // 人工成本（元）
    quantity: 500,            // 进货数量（件）
    orderAmount: 10000,       // 订单销售额（€）
    unitPrice: 100,           // 进货单价（元）
    targetProfitRate: 52,     // 目标利润率（%）
    businessType: '出口业务'   // 业务类型
  });

  // 计算结果状态
  const [pricingResult, setPricingResult] = useState({});
  const [taxResult, setTaxResult] = useState({});

  // 实时计算
  useEffect(() => {
    const pricing = calculatePricing(params);
    const tax = calculateTax(params, pricing);
    
    setPricingResult(pricing);
    setTaxResult(tax);
  }, [params]);

  // 更新参数的处理函数
  const handleParamChange = (key, value) => {
    setParams(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return (
    <div className="calculator-container">
      <Card className="calculator-card">
        <Title level={2} className="calculator-title">
          外贸智能动态定价与税务计算器
        </Title>
        
        <Row gutter={[24, 24]}>
          {/* 输入区域 */}
          <Col xs={24} lg={12}>
            <InputSection 
              params={params}
              onParamChange={handleParamChange}
            />
          </Col>
          
          {/* 结果区域 */}
          <Col xs={24} lg={12}>
            <ResultSection 
              pricingResult={pricingResult}
              taxResult={taxResult}
            />
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default Calculator;
