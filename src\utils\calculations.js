// 外贸计算器核心计算逻辑

/**
 * 计算定价结果
 * @param {Object} params - 输入参数
 * @returns {Object} 定价计算结果
 */
export function calculatePricing(params) {
  const {
    transportCost,    // 运输费用（元）
    laborCost,        // 人工成本（元）
    quantity,         // 进货数量（件）
    orderAmount,      // 订单销售额（€）
    unitPrice,        // 进货单价（元）
    targetProfitRate, // 目标利润率（%）
    businessType      // 业务类型
  } = params;

  // 总成本计算
  const totalCost = transportCost + laborCost + (unitPrice * quantity);
  
  // 按目标利润率计算的销售额（欧元）
  const calculatedSalesAmount = totalCost / (1 - targetProfitRate / 100) / 7.5; // 假设汇率1€=7.5¥
  
  // 实际利润率计算
  const actualRevenue = orderAmount * 7.5; // 转换为人民币
  const actualProfit = actualRevenue - totalCost;
  const actualProfitRate = totalCost > 0 ? (actualProfit / totalCost) * 100 : 0;
  
  // 进货单价（欧元）
  const unitPriceEuro = unitPrice / 7.5;

  return {
    salesAmount: orderAmount,           // 销售额（€）
    unitPriceEuro: unitPriceEuro,      // 进货单价（€）
    profitRate: actualProfitRate,       // 利润率（%）
    totalCost: totalCost,              // 总成本（¥）
    actualProfit: actualProfit,        // 实际利润（¥）
    calculatedSalesAmount: calculatedSalesAmount // 建议销售额（€）
  };
}

/**
 * 计算税务结果
 * @param {Object} params - 输入参数和定价结果
 * @returns {Object} 税务计算结果
 */
export function calculateTax(params, pricingResult) {
  const {
    businessType,
    orderAmount
  } = params;

  const revenue = orderAmount * 7.5; // 转换为人民币
  
  // 增值税计算（出口业务通常免征增值税，这里按一般情况计算）
  const vatRate = businessType === '出口业务' ? 0 : 0.13; // 13%增值税率
  const vat = revenue * vatRate;
  
  // 附加税（城建税7%，教育费附加3%，地方教育附加2%）
  const additionalTaxRate = 0.12; // 总计12%
  const additionalTax = vat * additionalTaxRate;
  
  // 印花税（合同金额的万分之三）
  const stampTax = revenue * 0.0003;
  
  // 企业所得税（利润的25%）
  const corporateIncomeTaxRate = 0.25;
  const taxableProfit = Math.max(0, pricingResult.actualProfit - vat - additionalTax - stampTax);
  const corporateIncomeTax = taxableProfit * corporateIncomeTaxRate;
  
  // 税后净利润
  const netProfitAfterTax = pricingResult.actualProfit - vat - additionalTax - stampTax - corporateIncomeTax;

  return {
    vat: vat,                                    // 增值税
    additionalTax: additionalTax,                // 附加税
    stampTax: stampTax,                          // 印花税
    corporateIncomeTax: corporateIncomeTax,      // 企业所得税
    netProfitAfterTax: netProfitAfterTax         // 税后净利润
  };
}

/**
 * 格式化货币显示
 * @param {number} amount - 金额
 * @param {string} currency - 货币符号
 * @returns {string} 格式化后的金额
 */
export function formatCurrency(amount, currency = '¥') {
  if (isNaN(amount)) return `${currency} 0.00`;
  return `${currency} ${amount.toFixed(2)}`;
}

/**
 * 格式化百分比显示
 * @param {number} rate - 百分比数值
 * @returns {string} 格式化后的百分比
 */
export function formatPercentage(rate) {
  if (isNaN(rate)) return '0%';
  return `${rate.toFixed(1)}%`;
}
