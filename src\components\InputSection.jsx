import React from 'react';
import { Card, InputNumber, Slider, Select, Row, Col, Typography, Space } from 'antd';

const { Title, Text } = Typography;
const { Option } = Select;

const InputSection = ({ params, onParamChange }) => {
  const inputStyle = { width: '100%' };
  
  return (
    <Card title="参数设置" className="input-section">
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        
        {/* 运输费用 */}
        <div>
          <Text strong>运输费用 (元):</Text>
          <InputNumber
            style={inputStyle}
            value={params.transportCost}
            onChange={(value) => onParamChange('transportCost', value || 0)}
            min={0}
            step={100}
            formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            parser={value => value.replace(/\$\s?|(,*)/g, '')}
          />
        </div>

        {/* 人工成本 */}
        <div>
          <Text strong>人工成本 (元):</Text>
          <InputNumber
            style={inputStyle}
            value={params.laborCost}
            onChange={(value) => onParamChange('laborCost', value || 0)}
            min={0}
            step={100}
            formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            parser={value => value.replace(/\$\s?|(,*)/g, '')}
          />
        </div>

        {/* 进货数量 */}
        <div>
          <Text strong>进货数量 (件):</Text>
          <InputNumber
            style={inputStyle}
            value={params.quantity}
            onChange={(value) => onParamChange('quantity', value || 0)}
            min={0}
            step={10}
            formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            parser={value => value.replace(/\$\s?|(,*)/g, '')}
          />
        </div>

        {/* 订单销售额 */}
        <div>
          <Text strong>订单销售额 (€):</Text>
          <Row gutter={8}>
            <Col span={12}>
              <InputNumber
                style={inputStyle}
                value={params.orderAmount}
                onChange={(value) => onParamChange('orderAmount', value || 0)}
                min={0}
                step={1000}
                formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value.replace(/\$\s?|(,*)/g, '')}
              />
            </Col>
            <Col span={12}>
              <Slider
                value={params.orderAmount}
                onChange={(value) => onParamChange('orderAmount', value)}
                min={0}
                max={50000}
                step={1000}
              />
            </Col>
          </Row>
        </div>

        {/* 进货单价 */}
        <div>
          <Text strong>进货单价 (元):</Text>
          <Row gutter={8}>
            <Col span={12}>
              <InputNumber
                style={inputStyle}
                value={params.unitPrice}
                onChange={(value) => onParamChange('unitPrice', value || 0)}
                min={0}
                step={10}
                formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value.replace(/\$\s?|(,*)/g, '')}
              />
            </Col>
            <Col span={12}>
              <Slider
                value={params.unitPrice}
                onChange={(value) => onParamChange('unitPrice', value)}
                min={0}
                max={1000}
                step={10}
              />
            </Col>
          </Row>
        </div>

        {/* 目标利润率 */}
        <div>
          <Text strong>目标利润率 (%):</Text>
          <Row gutter={8}>
            <Col span={12}>
              <InputNumber
                style={inputStyle}
                value={params.targetProfitRate}
                onChange={(value) => onParamChange('targetProfitRate', value || 0)}
                min={0}
                max={100}
                step={1}
                formatter={value => `${value}%`}
                parser={value => value.replace('%', '')}
              />
            </Col>
            <Col span={12}>
              <Slider
                value={params.targetProfitRate}
                onChange={(value) => onParamChange('targetProfitRate', value)}
                min={0}
                max={100}
                step={1}
              />
            </Col>
          </Row>
        </div>

        {/* 业务类型 */}
        <div>
          <Text strong>业务类型:</Text>
          <Select
            style={inputStyle}
            value={params.businessType}
            onChange={(value) => onParamChange('businessType', value)}
          >
            <Option value="出口业务">出口业务</Option>
            <Option value="进口业务">进口业务</Option>
            <Option value="国内贸易">国内贸易</Option>
          </Select>
        </div>

      </Space>
    </Card>
  );
};

export default InputSection;
