.calculator-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.calculator-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.calculator-title {
  text-align: center;
  color: #1890ff;
  margin-bottom: 32px !important;
  font-weight: 600;
}

.input-section {
  height: fit-content;
}

.input-section .ant-card-head {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px 8px 0 0;
}

.input-section .ant-card-head-title {
  color: white;
  font-weight: 600;
}

.result-card {
  margin-bottom: 16px;
  border-radius: 12px;
  overflow: hidden;
}

.pricing-result .ant-card-head {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.tax-result .ant-card-head {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.net-profit-result {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  border: 2px solid #52c41a;
}

.pricing-result .ant-card-head-title,
.tax-result .ant-card-head-title {
  color: white;
  font-weight: 600;
}

.ant-statistic-title {
  font-weight: 500;
  color: #666;
}

.ant-statistic-content {
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .calculator-container {
    padding: 10px;
  }
  
  .calculator-title {
    font-size: 20px !important;
  }
  
  .result-card {
    margin-bottom: 12px;
  }
}

/* 滑块样式优化 */
.ant-slider-track {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.ant-slider-handle {
  border: 2px solid #667eea;
}

.ant-slider-handle:hover,
.ant-slider-handle:focus {
  border-color: #764ba2;
  box-shadow: 0 0 0 5px rgba(102, 126, 234, 0.2);
}

/* 输入框样式优化 */
.ant-input-number {
  border-radius: 8px;
}

.ant-input-number:hover,
.ant-input-number:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* 选择器样式优化 */
.ant-select-selector {
  border-radius: 8px !important;
}

.ant-select:hover .ant-select-selector,
.ant-select-focused .ant-select-selector {
  border-color: #667eea !important;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
}
