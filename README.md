# 外贸智能动态定价与税务计算器

一个现代化的外贸计算器应用，支持实时动态计算定价、税务和净利润。

## 功能特性

### 📊 核心功能
- **实时计算**: 输入参数变化时自动重新计算所有结果
- **定价计算**: 基于成本和目标利润率计算建议售价
- **税务计算**: 自动计算增值税、附加税、印花税、企业所得税
- **净利润**: 显示税后净利润和实际利润率

### 📋 输入参数
- 运输费用（元）
- 人工成本（元）
- 进货数量（件）
- 订单销售额（€）
- 进货单价（元）
- 目标利润率（%）
- 业务类型（出口/进口/国内贸易）

### 📈 计算结果
- **定价结果**: 销售额、进货单价（欧元）、实际利润率
- **税务结果**: 各项税费明细
- **最终结果**: 税后净利润

## 技术栈

- **前端框架**: React 18 + Vite
- **UI 组件库**: Ant Design 5
- **开发语言**: JavaScript (ES6+)
- **样式**: CSS Modules + 自定义样式

## 项目结构

```
ForeignTradeCalculator/
├── src/
│   ├── components/
│   │   ├── Calculator.jsx          # 主计算器组件
│   │   ├── InputSection.jsx        # 输入参数区域
│   │   ├── ResultSection.jsx       # 结果显示区域
│   │   └── Calculator.css          # 样式文件
│   ├── utils/
│   │   └── calculations.js         # 核心计算逻辑
│   ├── App.jsx                     # 应用主组件
│   ├── App.css                     # 应用样式
│   ├── main.jsx                    # 应用入口
│   └── index.css                   # 全局样式
├── index.html                      # HTML 模板
├── package.json                    # 项目配置
├── vite.config.js                  # Vite 配置
└── README.md                       # 项目说明
```

## 安装和运行

### 前提条件
确保你的系统已安装 Node.js (版本 16 或更高)

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

应用将在 http://localhost:3000 启动

### 构建生产版本
```bash
npm run build
```

## 计算逻辑说明

### 定价计算
1. **总成本** = 运输费用 + 人工成本 + (进货单价 × 数量)
2. **建议销售额** = 总成本 ÷ (1 - 目标利润率) ÷ 汇率
3. **实际利润率** = (实际收入 - 总成本) ÷ 总成本 × 100%

### 税务计算
1. **增值税**: 收入 × 增值税率（出口业务免征）
2. **附加税**: 增值税 × 12%（城建税7% + 教育费附加3% + 地方教育附加2%）
3. **印花税**: 合同金额 × 0.03%
4. **企业所得税**: 应纳税所得额 × 25%
5. **税后净利润**: 利润 - 各项税费

## 特色功能

### 🎛️ 交互式界面
- 滑块和输入框双重控制
- 实时数据更新
- 响应式设计，支持移动端

### 📊 智能提示
- 利润率状态指示（优秀/良好/一般/偏低）
- 彩色编码的结果显示
- 清晰的数据分组

### 🎨 现代化设计
- 渐变背景和毛玻璃效果
- 流畅的动画过渡
- 专业的商务风格

## 自定义配置

你可以在 `src/utils/calculations.js` 中修改：
- 汇率设置（当前为 1€ = 7.5¥）
- 税率配置
- 计算公式

## 浏览器支持

- Chrome (推荐)
- Firefox
- Safari
- Edge

## 许可证

MIT License
