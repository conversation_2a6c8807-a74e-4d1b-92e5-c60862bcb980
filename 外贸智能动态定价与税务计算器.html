<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>外贸智能动态定价与税务计算器</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        
        .calculator-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            max-width: 1200px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px;
            border-radius: 20px 20px 0 0;
        }
        
        .input-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px;
        }
        
        .result-section {
            margin: 20px;
        }
        
        .result-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border-left: 5px solid;
        }
        
        .pricing-card {
            border-left-color: #007bff;
        }
        
        .tax-card {
            border-left-color: #fd7e14;
        }
        
        .profit-card {
            border-left-color: #28a745;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .slider-container {
            margin: 10px 0;
        }
        
        .slider {
            width: 100%;
            height: 8px;
            border-radius: 5px;
            background: #ddd;
            outline: none;
            -webkit-appearance: none;
        }
        
        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
        }
        
        .slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            border: none;
        }
        
        .result-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #495057;
        }
        
        .profit-rate-badge {
            font-size: 1.1em;
            padding: 8px 15px;
        }
        
        .net-profit-display {
            font-size: 2em;
            font-weight: bold;
            color: #28a745;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .calculator-container {
                margin: 10px;
            }
            
            .header h1 {
                font-size: 1.5rem;
            }
            
            .net-profit-display {
                font-size: 1.5em;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="calculator-container">
            <!-- 标题 -->
            <div class="header">
                <h1><i class="fas fa-calculator me-3"></i>外贸智能动态定价与税务计算器</h1>
                <p class="mb-0">实时计算定价、税务和净利润</p>
            </div>
            
            <div class="row">
                <!-- 输入区域 -->
                <div class="col-lg-6">
                    <div class="input-section">
                        <h4 class="mb-4"><i class="fas fa-cogs me-2"></i>参数设置</h4>
                        
                        <!-- 运输费用 -->
                        <div class="mb-4">
                            <label class="form-label"><strong>运输费用 (元):</strong></label>
                            <input type="number" class="form-control" id="transportCost" value="4000" min="0" step="100">
                        </div>
                        
                        <!-- 人工成本 -->
                        <div class="mb-4">
                            <label class="form-label"><strong>人工成本 (元):</strong></label>
                            <input type="number" class="form-control" id="laborCost" value="1000" min="0" step="100">
                        </div>
                        
                        <!-- 进货数量 -->
                        <div class="mb-4">
                            <label class="form-label"><strong>进货数量 (件):</strong></label>
                            <input type="number" class="form-control" id="quantity" value="500" min="0" step="10">
                        </div>
                        
                        <!-- 订单销售额 -->
                        <div class="mb-4">
                            <label class="form-label"><strong>订单销售额 (€):</strong></label>
                            <div class="row">
                                <div class="col-6">
                                    <input type="number" class="form-control" id="orderAmount" value="10000" min="0" step="1000">
                                </div>
                                <div class="col-6">
                                    <div class="slider-container">
                                        <input type="range" class="slider" id="orderAmountSlider" min="0" max="50000" value="10000" step="1000">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 进货单价 -->
                        <div class="mb-4">
                            <label class="form-label"><strong>进货单价 (元):</strong></label>
                            <div class="row">
                                <div class="col-6">
                                    <input type="number" class="form-control" id="unitPrice" value="100" min="0" step="10">
                                </div>
                                <div class="col-6">
                                    <div class="slider-container">
                                        <input type="range" class="slider" id="unitPriceSlider" min="0" max="1000" value="100" step="10">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 目标利润率 -->
                        <div class="mb-4">
                            <label class="form-label"><strong>目标利润率 (%):</strong></label>
                            <div class="row">
                                <div class="col-6">
                                    <input type="number" class="form-control" id="targetProfitRate" value="52" min="0" max="100" step="1">
                                </div>
                                <div class="col-6">
                                    <div class="slider-container">
                                        <input type="range" class="slider" id="targetProfitRateSlider" min="0" max="100" value="52" step="1">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 业务类型 -->
                        <div class="mb-4">
                            <label class="form-label"><strong>业务类型:</strong></label>
                            <select class="form-select" id="businessType">
                                <option value="出口业务">出口业务</option>
                                <option value="进口业务">进口业务</option>
                                <option value="国内贸易">国内贸易</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- 结果区域 -->
                <div class="col-lg-6">
                    <div class="result-section">
                        <!-- 定价计算结果 -->
                        <div class="result-card pricing-card">
                            <h5><i class="fas fa-chart-line me-2"></i>定价计算结果:</h5>
                            <div class="row">
                                <div class="col-6">
                                    <p class="mb-2">销售额: <span class="result-value" id="salesAmount">€ 10000.00</span></p>
                                </div>
                                <div class="col-6">
                                    <p class="mb-2">进货单价: <span class="result-value" id="unitPriceEuro">€ 117.50</span></p>
                                </div>
                                <div class="col-12 text-center mt-2">
                                    <span class="badge profit-rate-badge" id="profitRateBadge">利润率: 15%</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 税务计算结果 -->
                        <div class="result-card tax-card">
                            <h5><i class="fas fa-receipt me-2"></i>税务计算结果:</h5>
                            <div class="row">
                                <div class="col-6">
                                    <p class="mb-2">增值税: <span class="result-value" id="vat">¥ 0.00</span></p>
                                    <p class="mb-2">印花税: <span class="result-value" id="stampTax">¥ 22.50</span></p>
                                </div>
                                <div class="col-6">
                                    <p class="mb-2">附加税: <span class="result-value" id="additionalTax">¥ 0.00</span></p>
                                    <p class="mb-2">企业所得税: <span class="result-value" id="corporateIncomeTax">¥ 562.50</span></p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 税后净利润 -->
                        <div class="result-card profit-card">
                            <h5 class="text-center"><i class="fas fa-coins me-2"></i>税后净利润</h5>
                            <div class="net-profit-display" id="netProfitAfterTax">¥ 10665.00</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // 汇率设置 (1€ = 7.5¥)
        const EXCHANGE_RATE = 7.5;

        // 获取所有输入元素
        const inputs = {
            transportCost: document.getElementById('transportCost'),
            laborCost: document.getElementById('laborCost'),
            quantity: document.getElementById('quantity'),
            orderAmount: document.getElementById('orderAmount'),
            unitPrice: document.getElementById('unitPrice'),
            targetProfitRate: document.getElementById('targetProfitRate'),
            businessType: document.getElementById('businessType'),

            // 滑块元素
            orderAmountSlider: document.getElementById('orderAmountSlider'),
            unitPriceSlider: document.getElementById('unitPriceSlider'),
            targetProfitRateSlider: document.getElementById('targetProfitRateSlider')
        };

        // 获取所有结果显示元素
        const results = {
            salesAmount: document.getElementById('salesAmount'),
            unitPriceEuro: document.getElementById('unitPriceEuro'),
            profitRateBadge: document.getElementById('profitRateBadge'),
            vat: document.getElementById('vat'),
            additionalTax: document.getElementById('additionalTax'),
            stampTax: document.getElementById('stampTax'),
            corporateIncomeTax: document.getElementById('corporateIncomeTax'),
            netProfitAfterTax: document.getElementById('netProfitAfterTax')
        };

        // 计算定价结果
        function calculatePricing(params) {
            const {
                transportCost,
                laborCost,
                quantity,
                orderAmount,
                unitPrice,
                targetProfitRate,
                businessType
            } = params;

            // 总成本计算
            const totalCost = transportCost + laborCost + (unitPrice * quantity);

            // 实际收入（转换为人民币）
            const actualRevenue = orderAmount * EXCHANGE_RATE;

            // 实际利润
            const actualProfit = actualRevenue - totalCost;

            // 实际利润率
            const actualProfitRate = totalCost > 0 ? (actualProfit / totalCost) * 100 : 0;

            // 进货单价（欧元）
            const unitPriceEuro = unitPrice / EXCHANGE_RATE;

            return {
                salesAmount: orderAmount,
                unitPriceEuro: unitPriceEuro,
                profitRate: actualProfitRate,
                totalCost: totalCost,
                actualProfit: actualProfit,
                actualRevenue: actualRevenue
            };
        }

        // 计算税务结果
        function calculateTax(params, pricingResult) {
            const { businessType } = params;
            const { actualRevenue, actualProfit } = pricingResult;

            // 增值税计算（出口业务免征增值税）
            const vatRate = businessType === '出口业务' ? 0 : 0.13;
            const vat = actualRevenue * vatRate;

            // 附加税（城建税7% + 教育费附加3% + 地方教育附加2% = 12%）
            const additionalTaxRate = 0.12;
            const additionalTax = vat * additionalTaxRate;

            // 印花税（合同金额的万分之三）
            const stampTax = actualRevenue * 0.0003;

            // 企业所得税（应纳税所得额的25%）
            const taxableProfit = Math.max(0, actualProfit - vat - additionalTax - stampTax);
            const corporateIncomeTax = taxableProfit * 0.25;

            // 税后净利润
            const netProfitAfterTax = actualProfit - vat - additionalTax - stampTax - corporateIncomeTax;

            return {
                vat,
                additionalTax,
                stampTax,
                corporateIncomeTax,
                netProfitAfterTax
            };
        }

        // 格式化货币显示
        function formatCurrency(amount, currency = '¥') {
            if (isNaN(amount)) return `${currency} 0.00`;
            return `${currency} ${amount.toFixed(2)}`;
        }

        // 格式化百分比显示
        function formatPercentage(rate) {
            if (isNaN(rate)) return '0%';
            return `${rate.toFixed(1)}%`;
        }

        // 获取利润率状态
        function getProfitRateStatus(rate) {
            if (rate >= 50) return { class: 'bg-success', text: '优秀' };
            if (rate >= 30) return { class: 'bg-primary', text: '良好' };
            if (rate >= 10) return { class: 'bg-warning', text: '一般' };
            return { class: 'bg-danger', text: '偏低' };
        }

        // 更新计算结果
        function updateResults() {
            // 获取当前输入值
            const params = {
                transportCost: parseFloat(inputs.transportCost.value) || 0,
                laborCost: parseFloat(inputs.laborCost.value) || 0,
                quantity: parseFloat(inputs.quantity.value) || 0,
                orderAmount: parseFloat(inputs.orderAmount.value) || 0,
                unitPrice: parseFloat(inputs.unitPrice.value) || 0,
                targetProfitRate: parseFloat(inputs.targetProfitRate.value) || 0,
                businessType: inputs.businessType.value
            };

            // 计算结果
            const pricingResult = calculatePricing(params);
            const taxResult = calculateTax(params, pricingResult);

            // 更新定价结果显示
            results.salesAmount.textContent = formatCurrency(pricingResult.salesAmount, '€');
            results.unitPriceEuro.textContent = formatCurrency(pricingResult.unitPriceEuro, '€');

            // 更新利润率显示
            const profitStatus = getProfitRateStatus(pricingResult.profitRate);
            results.profitRateBadge.textContent = `利润率: ${formatPercentage(pricingResult.profitRate)} (${profitStatus.text})`;
            results.profitRateBadge.className = `badge profit-rate-badge ${profitStatus.class}`;

            // 更新税务结果显示
            results.vat.textContent = formatCurrency(taxResult.vat);
            results.additionalTax.textContent = formatCurrency(taxResult.additionalTax);
            results.stampTax.textContent = formatCurrency(taxResult.stampTax);
            results.corporateIncomeTax.textContent = formatCurrency(taxResult.corporateIncomeTax);

            // 更新税后净利润显示
            results.netProfitAfterTax.textContent = formatCurrency(taxResult.netProfitAfterTax);
        }

        // 同步滑块和输入框
        function syncSliderAndInput(inputId, sliderId) {
            const input = document.getElementById(inputId);
            const slider = document.getElementById(sliderId);

            input.addEventListener('input', function() {
                slider.value = this.value;
                updateResults();
            });

            slider.addEventListener('input', function() {
                input.value = this.value;
                updateResults();
            });
        }

        // 初始化事件监听器
        function initializeEventListeners() {
            // 同步滑块和输入框
            syncSliderAndInput('orderAmount', 'orderAmountSlider');
            syncSliderAndInput('unitPrice', 'unitPriceSlider');
            syncSliderAndInput('targetProfitRate', 'targetProfitRateSlider');

            // 为所有输入元素添加事件监听器
            Object.values(inputs).forEach(input => {
                if (input && !input.id.includes('Slider')) {
                    input.addEventListener('input', updateResults);
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
            updateResults(); // 初始计算
        });
    </script>
</body>
</html>
