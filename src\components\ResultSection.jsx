import React from 'react';
import { Card, Statistic, Row, Col, Typography, Divider, Tag } from 'antd';
import { formatCurrency, formatPercentage } from '../utils/calculations';

const { Title, Text } = Typography;

const ResultSection = ({ pricingResult, taxResult }) => {
  // 判断利润率状态
  const getProfitRateStatus = (rate) => {
    if (rate >= 50) return { color: 'success', text: '优秀' };
    if (rate >= 30) return { color: 'processing', text: '良好' };
    if (rate >= 10) return { color: 'warning', text: '一般' };
    return { color: 'error', text: '偏低' };
  };

  const profitStatus = getProfitRateStatus(pricingResult.profitRate || 0);

  return (
    <div>
      {/* 定价计算结果 */}
      <Card title="定价计算结果:" className="result-card pricing-result">
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Statistic
              title="销售额"
              value={pricingResult.salesAmount || 0}
              precision={2}
              suffix="€"
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="进货单价"
              value={pricingResult.unitPriceEuro || 0}
              precision={2}
              suffix="€"
              valueStyle={{ color: '#722ed1' }}
            />
          </Col>
          <Col span={24}>
            <div style={{ textAlign: 'center' }}>
              <Text strong>利润率: </Text>
              <Tag color={profitStatus.color} style={{ fontSize: '16px', padding: '4px 12px' }}>
                {formatPercentage(pricingResult.profitRate || 0)} ({profitStatus.text})
              </Tag>
            </div>
          </Col>
        </Row>
      </Card>

      <Divider />

      {/* 税务计算结果 */}
      <Card title="税务计算结果:" className="result-card tax-result">
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Statistic
              title="增值税"
              value={taxResult.vat || 0}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#fa541c' }}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="附加税"
              value={taxResult.additionalTax || 0}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#fa541c' }}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="印花税"
              value={taxResult.stampTax || 0}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#fa541c' }}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="企业所得税"
              value={taxResult.corporateIncomeTax || 0}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#fa541c' }}
            />
          </Col>
        </Row>
      </Card>

      <Divider />

      {/* 税后净利润 */}
      <Card className="result-card net-profit-result">
        <div style={{ textAlign: 'center' }}>
          <Title level={3} style={{ color: '#52c41a', margin: 0 }}>
            税后净利润: {formatCurrency(taxResult.netProfitAfterTax || 0)}
          </Title>
        </div>
      </Card>
    </div>
  );
};

export default ResultSection;
